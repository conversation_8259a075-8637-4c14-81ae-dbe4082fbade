<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">bitchat</string>
    <string name="permission_bluetooth_rationale">Bluetooth permission is required for peer-to-peer messaging without internet.</string>
    <string name="permission_location_rationale">Location permission is required to discover nearby devices via Bluetooth.</string>
    <string name="permission_notification_rationale">Notification permission is required to alert you of new messages.</string>
    <string name="nickname_hint">nickname</string>
    <string name="message_hint">type a message…</string>
    <string name="channel_password_hint">Password</string>
    <string name="join_channel">Join Channel</string>
    <string name="leave_channel">Leave</string>
    <string name="send_message">Send</string>
    <string name="back">Back</string>
    <string name="people">People</string>
    <string name="channels">Channels</string>
    <string name="online_users">Online Users</string>
    <string name="no_one_connected">No one connected</string>
    <string name="emergency_clear_hint">Triple tap to clear all data</string>
    <string name="your_network">Network</string>
    
    <!-- Battery Optimization Strings -->
    <string name="battery_optimization_detected">Battery Optimization Detected</string>
    <string name="battery_optimization_disabled">Battery Optimization Disabled</string>
    <string name="battery_optimization_not_required">Battery Optimization Not Required</string>
    <string name="battery_optimization_checking">Checking Battery Optimization</string>
    <string name="battery_optimization_why_disable">Why disable battery optimization?</string>
    <string name="battery_optimization_explanation">bitchat runs in the background to maintain mesh network connections with nearby devices. Battery optimization can interrupt these connections, causing messages to be delayed or missed.\n\nDisabling battery optimization ensures reliable peer-to-peer messaging.</string>
    <string name="battery_optimization_disable_button">Disable Battery Optimization</string>
    <string name="battery_optimization_note">Note: You can change this setting later in Android Settings > Apps > bitchat > Battery</string>
    <string name="battery_optimization_not_supported_explanation">Your device doesn\'t require battery optimization settings. bitchat will run normally.</string>
    <string name="battery_optimization_not_supported_message">Your device doesn\'t require battery optimization settings. bitchat will run normally.</string>
    <string name="battery_optimization_success_message">bitchat can run reliably in the background</string>
    <string name="battery_optimization_benefits">• Ensures reliable message delivery\n• Maintains mesh network connectivity\n• Allows background message relay\n• Prevents connection drops</string>
    <string name="battery_optimization_check_again">Check Again</string>
    <string name="battery_optimization_skip">Skip for Now</string>
    <string name="battery_optimization_continue">Continue</string>
    <string name="retry">Retry</string>
    <string name="skip">Skip</string>
</resources>
