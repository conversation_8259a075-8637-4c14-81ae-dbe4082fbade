<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 8.2.0" type="baseline" client="gradle" dependencies="false" name="AGP (8.2.0)" variant="all" version="8.2.0">

    <issue
        id="MissingPermission"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        errorLine1="                    gatt.disconnect()"
        errorLine2="                    ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bitchat/android/mesh/BluetoothMeshService.kt"
            line="175"
            column="21"/>
    </issue>

    <issue
        id="MissingPermission"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        errorLine1="                    gatt.close()"
        errorLine2="                    ~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bitchat/android/mesh/BluetoothMeshService.kt"
            line="176"
            column="21"/>
    </issue>

    <issue
        id="MissingPermission"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        errorLine1="            gattServer?.close()"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bitchat/android/mesh/BluetoothMeshService.kt"
            line="187"
            column="13"/>
    </issue>

    <issue
        id="MissingPermission"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        errorLine1="                        gattServer?.sendResponse(device, requestId, BluetoothGatt.GATT_SUCCESS, 0, null)"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bitchat/android/mesh/BluetoothMeshService.kt"
            line="268"
            column="25"/>
    </issue>

    <issue
        id="MissingPermission"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        errorLine1="                    gattServer?.sendResponse(device, requestId, BluetoothGatt.GATT_SUCCESS, 0, null)"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bitchat/android/mesh/BluetoothMeshService.kt"
            line="296"
            column="21"/>
    </issue>

    <issue
        id="MissingPermission"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        errorLine1="        gattServer = bluetoothManager.openGattServer(context, serverCallback)"
        errorLine2="                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bitchat/android/mesh/BluetoothMeshService.kt"
            line="301"
            column="22"/>
    </issue>

    <issue
        id="MissingPermission"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        errorLine1="        gattServer?.addService(service)"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bitchat/android/mesh/BluetoothMeshService.kt"
            line="325"
            column="9"/>
    </issue>

    <issue
        id="MissingPermission"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        errorLine1="        bleAdvertiser?.startAdvertising(settings, data, advertiseCallback)"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bitchat/android/mesh/BluetoothMeshService.kt"
            line="384"
            column="9"/>
    </issue>

    <issue
        id="MissingPermission"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        errorLine1="            bleAdvertiser.startAdvertising(settings, data, scanResponse, advertiseCallback)"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bitchat/android/mesh/BluetoothMeshService.kt"
            line="429"
            column="13"/>
    </issue>

    <issue
        id="MissingPermission"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        errorLine1="            bleAdvertiser.startAdvertising(settings, data, advertiseCallback)"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bitchat/android/mesh/BluetoothMeshService.kt"
            line="466"
            column="13"/>
    </issue>

    <issue
        id="MissingPermission"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        errorLine1="        bleAdvertiser.stopAdvertising(object : AdvertiseCallback() {})"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bitchat/android/mesh/BluetoothMeshService.kt"
            line="478"
            column="9"/>
    </issue>

    <issue
        id="MissingPermission"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        errorLine1="            bleScanner.startScan(listOf(scanFilter), scanSettings, scanCallback)"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bitchat/android/mesh/BluetoothMeshService.kt"
            line="526"
            column="13"/>
    </issue>

    <issue
        id="MissingPermission"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        errorLine1="        bleScanner.stopScan(object : ScanCallback() {})"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bitchat/android/mesh/BluetoothMeshService.kt"
            line="539"
            column="9"/>
    </issue>

    <issue
        id="MissingPermission"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        errorLine1="                        gatt.discoverServices()"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bitchat/android/mesh/BluetoothMeshService.kt"
            line="626"
            column="25"/>
    </issue>

    <issue
        id="MissingPermission"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        errorLine1="                        gatt.close()"
        errorLine2="                        ~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bitchat/android/mesh/BluetoothMeshService.kt"
            line="633"
            column="25"/>
    </issue>

    <issue
        id="MissingPermission"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        errorLine1="                        gatt.setCharacteristicNotification(characteristic, true)"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bitchat/android/mesh/BluetoothMeshService.kt"
            line="645"
            column="25"/>
    </issue>

    <issue
        id="MissingPermission"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        errorLine1="                        gatt.writeDescriptor(descriptor)"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bitchat/android/mesh/BluetoothMeshService.kt"
            line="652"
            column="25"/>
    </issue>

    <issue
        id="MissingPermission"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        errorLine1="        device.connectGatt(context, false, gattCallback)"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bitchat/android/mesh/BluetoothMeshService.kt"
            line="683"
            column="9"/>
    </issue>

    <issue
        id="MissingPermission"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        errorLine1="            gattServer?.notifyCharacteristicChanged(device, characteristic, false)"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bitchat/android/mesh/BluetoothMeshService.kt"
            line="700"
            column="13"/>
    </issue>

    <issue
        id="MissingPermission"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        errorLine1="                gatt.writeCharacteristic(characteristic)"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bitchat/android/mesh/BluetoothMeshService.kt"
            line="721"
            column="17"/>
    </issue>

    <issue
        id="MissingPermission"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        errorLine1="                    val success = gattServer?.notifyCharacteristicChanged(device, char, false) ?: false"
        errorLine2="                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bitchat/android/mesh/BluetoothMeshService.kt"
            line="1135"
            column="35"/>
    </issue>

    <issue
        id="MissingPermission"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        errorLine1="                    val success = gatt.writeCharacteristic(characteristic)"
        errorLine2="                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bitchat/android/mesh/BluetoothMeshService.kt"
            line="1153"
            column="35"/>
    </issue>

    <issue
        id="OldTargetApi"
        message="Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the android.os.Build.VERSION_CODES javadoc for details."
        errorLine1="        targetSdk = 34"
        errorLine2="        ~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="14"
            column="9"/>
    </issue>

    <issue
        id="RedundantLabel"
        message="Redundant label can be removed"
        errorLine1="            android:label=&quot;@string/app_name&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="35"
            column="13"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.core:core-ktx than 1.12.0 is available: 1.16.0"
        errorLine1="    implementation(&quot;androidx.core:core-ktx:1.12.0&quot;)"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="59"
            column="21"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.lifecycle:lifecycle-runtime-ktx than 2.7.0 is available: 2.9.1"
        errorLine1="    implementation(&quot;androidx.lifecycle:lifecycle-runtime-ktx:2.7.0&quot;)"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="60"
            column="21"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.activity:activity-compose than 1.8.2 is available: 1.10.1"
        errorLine1="    implementation(&quot;androidx.activity:activity-compose:1.8.2&quot;)"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="61"
            column="21"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.appcompat:appcompat than 1.6.1 is available: 1.7.1"
        errorLine1="    implementation(&quot;androidx.appcompat:appcompat:1.6.1&quot;)"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="69"
            column="21"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.lifecycle:lifecycle-viewmodel-compose than 2.7.0 is available: 2.9.1"
        errorLine1="    implementation(&quot;androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0&quot;)"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="72"
            column="21"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.lifecycle:lifecycle-livedata-ktx than 2.7.0 is available: 2.9.1"
        errorLine1="    implementation(&quot;androidx.lifecycle:lifecycle-livedata-ktx:2.7.0&quot;)"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="73"
            column="21"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.navigation:navigation-compose than 2.7.6 is available: 2.9.1"
        errorLine1="    implementation(&quot;androidx.navigation:navigation-compose:2.7.6&quot;)"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="77"
            column="21"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.security:security-crypto than 1.1.0-alpha06 is available: 1.1.0-beta01"
        errorLine1="    implementation(&quot;androidx.security:security-crypto:1.1.0-alpha06&quot;)"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="99"
            column="21"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1"
        errorLine1="    androidTestImplementation(&quot;androidx.test.ext:junit:1.1.5&quot;)"
        errorLine2="                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="103"
            column="32"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1"
        errorLine1="    androidTestImplementation(&quot;androidx.test.espresso:espresso-core:3.5.1&quot;)"
        errorLine2="                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="104"
            column="32"/>
    </issue>

    <issue
        id="LockedOrientationActivity"
        message="Expecting `android:screenOrientation=&quot;unspecified&quot;` or `&quot;fullSensor&quot;` for this activity so the user can use the application in any orientation and provide a great experience on Chrome OS devices"
        errorLine1="            android:screenOrientation=&quot;portrait&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="37"
            column="13"/>
    </issue>

    <issue
        id="StaticFieldLeak"
        message="This field leaks a context object"
        errorLine1="    private val context: Context = application.applicationContext"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bitchat/android/ui/ChatViewModel.kt"
            line="30"
            column="5"/>
    </issue>

    <issue
        id="AutoboxingStateCreation"
        message="Prefer `mutableIntStateOf` instead of `mutableStateOf`"
        errorLine1="    var tripleClickCount by remember { mutableStateOf(0) }"
        errorLine2="                                       ~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bitchat/android/ui/ChatScreen.kt"
            line="62"
            column="40"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.permission_bluetooth_rationale` appears to be unused"
        errorLine1="    &lt;string name=&quot;permission_bluetooth_rationale&quot;>Bluetooth permission is required for peer-to-peer messaging without internet.&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="4"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.permission_location_rationale` appears to be unused"
        errorLine1="    &lt;string name=&quot;permission_location_rationale&quot;>Location permission is required to discover nearby devices via Bluetooth.&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="5"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.permission_notification_rationale` appears to be unused"
        errorLine1="    &lt;string name=&quot;permission_notification_rationale&quot;>Notification permission is required to alert you of new messages.&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="6"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.nickname_hint` appears to be unused"
        errorLine1="    &lt;string name=&quot;nickname_hint&quot;>nickname&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="7"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.message_hint` appears to be unused"
        errorLine1="    &lt;string name=&quot;message_hint&quot;>type a message…&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="8"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.channel_password_hint` appears to be unused"
        errorLine1="    &lt;string name=&quot;channel_password_hint&quot;>Password&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="9"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.join_channel` appears to be unused"
        errorLine1="    &lt;string name=&quot;join_channel&quot;>Join Channel&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="10"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.leave_channel` appears to be unused"
        errorLine1="    &lt;string name=&quot;leave_channel&quot;>Leave&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="11"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.send_message` appears to be unused"
        errorLine1="    &lt;string name=&quot;send_message&quot;>Send&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="12"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.back` appears to be unused"
        errorLine1="    &lt;string name=&quot;back&quot;>Back&lt;/string>"
        errorLine2="            ~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="13"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.people` appears to be unused"
        errorLine1="    &lt;string name=&quot;people&quot;>People&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="14"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.channels` appears to be unused"
        errorLine1="    &lt;string name=&quot;channels&quot;>Channels&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="15"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.online_users` appears to be unused"
        errorLine1="    &lt;string name=&quot;online_users&quot;>Online Users&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="16"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.no_one_connected` appears to be unused"
        errorLine1="    &lt;string name=&quot;no_one_connected&quot;>No one connected&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="17"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.emergency_clear_hint` appears to be unused"
        errorLine1="    &lt;string name=&quot;emergency_clear_hint&quot;>Triple tap to clear all data&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="18"
            column="13"/>
    </issue>

    <issue
        id="MissingApplicationIcon"
        message="Should explicitly set `android:icon`, there is no default"
        errorLine1="    &lt;application"
        errorLine2="     ~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="23"
            column="6"/>
    </issue>

    <issue
        id="NullSafeMutableLiveData"
        message="Expected non-nullable value"
        errorLine1="            _nickname.value = savedNickname"
        errorLine2="                              ~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bitchat/android/ui/ChatViewModel.kt"
            line="125"
            column="31"/>
    </issue>

</issues>
