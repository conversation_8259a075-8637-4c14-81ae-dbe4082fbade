[versions]
# Android and Kotlin
agp = "8.10.1"
kotlin = "2.2.0"
compileSdk = "35"
minSdk = "26" # API 26 for proper BLE support
targetSdk = "34"

# AndroidX Core
core-ktx = "1.16.0"
lifecycle-runtime = "2.9.1"
activity-compose = "1.10.1"
appcompat = "1.7.1"

# Compose
compose-bom = "2025.06.01"

# Navigation
navigation-compose = "2.9.1"

# Accompanist
accompanist-permissions = "0.37.3"

# Cryptography
bouncycastle = "1.70"
tink-android = "1.10.0"

# JSON
gson = "2.13.1"

# Coroutines
kotlinx-coroutines = "1.10.2"

# Bluetooth
nordic-ble = "2.6.1"

# Compression
lz4-java = "1.8.0"

# Security
security-crypto = "1.1.0-beta01"

# Testing
junit = "4.13.2"
androidx-test-ext = "1.2.1"
espresso = "3.6.1"

[libraries]
# AndroidX Core
androidx-core-ktx = { module = "androidx.core:core-ktx", version.ref = "core-ktx" }
androidx-lifecycle-runtime-ktx = { module = "androidx.lifecycle:lifecycle-runtime-ktx", version.ref = "lifecycle-runtime" }
androidx-activity-compose = { module = "androidx.activity:activity-compose", version.ref = "activity-compose" }
androidx-appcompat = { module = "androidx.appcompat:appcompat", version.ref = "appcompat" }

# Compose
androidx-compose-bom = { module = "androidx.compose:compose-bom", version.ref = "compose-bom" }
androidx-compose-ui = { module = "androidx.compose.ui:ui" }
androidx-compose-ui-graphics = { module = "androidx.compose.ui:ui-graphics" }
androidx-compose-ui-tooling = { module = "androidx.compose.ui:ui-tooling" }
androidx-compose-ui-tooling-preview = { module = "androidx.compose.ui:ui-tooling-preview" }
androidx-compose-material3 = { module = "androidx.compose.material3:material3" }
androidx-compose-runtime-livedata = { module = "androidx.compose.runtime:runtime-livedata" }
androidx-compose-material-icons-extended = { module = "androidx.compose.material:material-icons-extended" }

# Lifecycle
androidx-lifecycle-viewmodel-compose = { module = "androidx.lifecycle:lifecycle-viewmodel-compose", version.ref = "lifecycle-runtime" }
androidx-lifecycle-livedata-ktx = { module = "androidx.lifecycle:lifecycle-livedata-ktx", version.ref = "lifecycle-runtime" }

# Navigation
androidx-navigation-compose = { module = "androidx.navigation:navigation-compose", version.ref = "navigation-compose" }

# Accompanist
accompanist-permissions = { module = "com.google.accompanist:accompanist-permissions", version.ref = "accompanist-permissions" }

# Cryptography
bouncycastle-bcprov = { module = "org.bouncycastle:bcprov-jdk15on", version.ref = "bouncycastle" }
google-tink-android = { module = "com.google.crypto.tink:tink-android", version.ref = "tink-android" }

# JSON
gson = { module = "com.google.code.gson:gson", version.ref = "gson" }

# Coroutines
kotlinx-coroutines-android = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-android", version.ref = "kotlinx-coroutines" }

# Bluetooth
nordic-ble = { module = "no.nordicsemi.android:ble", version.ref = "nordic-ble" }

# Compression
lz4-java = { module = "org.lz4:lz4-java", version.ref = "lz4-java" }

# Security
androidx-security-crypto = { module = "androidx.security:security-crypto", version.ref = "security-crypto" }

# Testing
junit = { module = "junit:junit", version.ref = "junit" }
androidx-test-ext-junit = { module = "androidx.test.ext:junit", version.ref = "androidx-test-ext" }
androidx-test-espresso-core = { module = "androidx.test.espresso:espresso-core", version.ref = "espresso" }
androidx-compose-ui-test-junit4 = { module = "androidx.compose.ui:ui-test-junit4" }
androidx-compose-ui-test-manifest = { module = "androidx.compose.ui:ui-test-manifest" }

[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
android-library = { id = "com.android.library", version.ref = "agp" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
kotlin-parcelize = { id = "kotlin-parcelize" }
kotlin-compose = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin" }

[bundles]
compose = [
    "androidx-compose-ui",
    "androidx-compose-ui-graphics",
    "androidx-compose-ui-tooling-preview",
    "androidx-compose-material3",
    "androidx-compose-runtime-livedata",
    "androidx-compose-material-icons-extended"
]

lifecycle = [
    "androidx-lifecycle-runtime-ktx",
    "androidx-lifecycle-viewmodel-compose",
    "androidx-lifecycle-livedata-ktx"
]

cryptography = [
    "bouncycastle-bcprov",
    "google-tink-android"
]

testing = [
    "junit",
    "androidx-test-ext-junit",
    "androidx-test-espresso-core"
]

compose-testing = [
    "androidx-compose-ui-test-junit4",
    "androidx-compose-ui-test-manifest"
] 