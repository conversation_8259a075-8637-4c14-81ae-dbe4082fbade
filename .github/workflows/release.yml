name: Release

on:
  workflow_dispatch:
  push:
    tags:
      - 'v*.*.*' # Triggers for tags like v1.0.0

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up JDK
        uses: actions/setup-java@v4
        with:
          distribution: temurin
          java-version: 17
    
      - name: Setup Gradle
        uses: gradle/gradle-build-action@v2
        with:
          gradle-version: wrapper

      - name: Cache Gradle files
        uses: actions/cache@v3
        with:
          path: |
            ~/.gradle/caches
            ~/.gradle/wrapper
          key: gradle-${{ runner.os }}-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
          restore-keys: |
            gradle-${{ runner.os }}-

      - name: Grant execute permission for Gradlew
        run: chmod +x ./gradlew

      - name: Build APK
        run: ./gradlew assembleRelease --no-daemon --stacktrace
      
      - name: List APK files
        run: |
          echo "APK files built:"
          find app/build/outputs/apk/release -name "*.apk" -type f

      - name: Rename APK
        run: |
          mv app/build/outputs/apk/release/app-release-unsigned.apk app/build/outputs/apk/release/bitchat.apk
      
      - name: DEBUG
        run: |
          set -x

          pwd

          ls -all

          cd app/build/outputs/
          ls -all
          tree
      
      # Optional: Sign APK (requires secrets)
      # - name: Sign APK
      #   uses: r0adkll/sign-android-release@v1
      #   with:
      #     releaseDirectory: app/build/outputs/apk/release
      #     signingKeyBase64: ${{ secrets.SIGNING_KEY }}
      #     alias: ${{ secrets.ALIAS }}
      #     keyStorePassword: ${{ secrets.KEY_STORE_PASSWORD }}
      #     keyPassword: ${{ secrets.KEY_PASSWORD }}

      - name: Upload APK as artifact
        uses: actions/upload-artifact@v4
        with:
          name: bitchat-release-apk-${{ github.ref_name }}
          path: app/build/outputs/apk/release/*.apk
          retention-days: 30
          if-no-files-found: error

  release:
    needs: build
    runs-on: ubuntu-latest

    steps:
      - name: Download APK artifact
        uses: actions/download-artifact@v4
        with:
          name: bitchat-release-apk-${{ github.ref_name }}
          path: .

      - name: Create GitHub Release
        uses: softprops/action-gh-release@v2
        with:
          files: bitchat.apk
          name: Release ${{ github.ref_name }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
