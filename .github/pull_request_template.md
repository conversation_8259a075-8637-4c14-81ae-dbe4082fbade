# Description

## Checklist
<!--
  To help us keep the issue tracker clean and work as efficient as possible,
  please make sure that you have done all of the following.
  You can tick the boxes below by placing an x inside the brackets like this: [x]
-->
- [ ] I have read the contribution guidelines: <https://github.com/permissionlesstech/bitchat-android?tab=readme-ov-file#contributing>
- [ ] I have performed a self-review of my code
<!-- - [ ] I have run the automated code checks using `./gradlew checkstyle spotbugsPlayDebug spotbugsDebug :app:lintPlayDebug` -->
- [ ] I have mentioned the corresponding issue and the relevant keyword (e.g., "Closes: #xy") in the description (see <https://docs.github.com/en/issues/tracking-your-work-with-issues/linking-a-pull-request-to-an-issue>)
- [ ] If it is a core feature, I have added automated tests
